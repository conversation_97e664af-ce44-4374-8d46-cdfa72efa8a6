#!/usr/bin/env python3
"""
处理 transcriptInfo_with_updated_locations.csv 文件：
1. 将12个UTR特征列的值转换为yes/no
2. 仅按“12个UTR特征”分组聚合
3. 对每个特征组合计算 transcript_count 与 gene_count，并保留二者相等的组合
4. 生成按特征聚合后的 CSV/JSON 输出（被过滤掉的组合不写出）
"""

import pandas as pd
import os
import sys
import json

def is_non_empty(value):
    """检查值是否非空"""
    if pd.isna(value):
        return False
    if isinstance(value, str):
        value = value.strip()
        if value == '' or value.lower() == 'nan' or value.lower() == 'na':
            return False
    return True

def convert_to_yes_no(value):
    """将值转换为yes或no"""
    return 'yes' if is_non_empty(value) else 'no'

def process_transcript_info(input_file, output_file, json_file, max_csv_transcripts=100, json_rows_file=None):
    """处理转录本信息文件"""
    
    print(f"开始处理文件: {input_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 {input_file}")
        return False
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file)
        print(f"成功读取文件，共 {len(df)} 行")
        print(f"列数: {len(df.columns)}")
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return False
    
    # 定义需要处理的12个UTR特征列
    utr_feature_columns = [
        'threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
        'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres',
        'fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
        'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres'
    ]
    
    # 检查必需的列是否存在
    missing_columns = []
    for col in utr_feature_columns + ['transcriptId', 'geneId']:
        if col not in df.columns:
            missing_columns.append(col)
    
    if missing_columns:
        print(f"错误: 缺少必需的列: {missing_columns}")
        return False
    
    print(f"找到所有必需的列")
    
    # 创建工作副本
    work_df = df.copy()
    
    # 转换UTR特征列为yes/no
    print(f"\n转换UTR特征列为yes/no...")
    for col in utr_feature_columns:
        original_non_empty = work_df[col].apply(is_non_empty).sum()
        work_df[col] = work_df[col].apply(convert_to_yes_no)
        yes_count = (work_df[col] == 'yes').sum()
        no_count = (work_df[col] == 'no').sum()
        
        print(f"  {col}: {original_non_empty} 非空 -> {yes_count} yes, {no_count} no")
    
    # 创建组合并统计（仅按特征）
    print(f"\n计算UTR特征组合(按特征)...")

    grouped = work_df.groupby(utr_feature_columns)
    print(f"找到 {len(grouped)} 种不同的UTR特征组合")

    # 结果容器（按特征）
    results = []  # CSV/行样式JSON（已过滤）
    all_combinations_data = {}  # 完整JSON（已过滤）

    kept_groups = 0
    total_groups = 0

    for combination, group in grouped:
        total_groups += 1
        # 获取该组合下的所有唯一transcriptId和geneId
        transcript_ids = group['transcriptId'].unique().tolist()
        gene_ids = group['geneId'].dropna().unique().tolist()

        # 过滤掉无效gene
        valid_gene_ids = [str(gid) for gid in gene_ids if gid is not None and str(gid) != 'nan']
        sorted_gene_ids = sorted(valid_gene_ids)
        sorted_transcript_ids = sorted(transcript_ids)

        transcript_count = len(sorted_transcript_ids)
        gene_count = len(sorted_gene_ids)

        # 筛选条件：transcript_count == gene_count
        if transcript_count != gene_count:
            continue

        kept_groups += 1

        # 为CSV限制transcript和gene数量
        limited_transcript_ids = sorted_transcript_ids[:max_csv_transcripts]
        limited_gene_ids = sorted_gene_ids[:max_csv_transcripts]
        transcript_ids_str = ';'.join(limited_transcript_ids)
        gene_ids_str = ';'.join(limited_gene_ids)

        # 创建结果行（仅三列：transcriptId, geneId, UTR_Features）
        features_binary = ','.join(['1' if val == 'yes' else '0' for val in combination])
        results.append({
            'transcriptId': transcript_ids_str,
            'geneId': gene_ids_str,
            'UTR_Features': features_binary,
        })

        # 为JSON保存完整数据
        combination_key = '|'.join(combination)
        combination_dict = {col: combination[i] for i, col in enumerate(utr_feature_columns)}
        all_combinations_data[combination_key] = {
            'features': combination_dict,
            'transcript_count': transcript_count,
            'transcript_ids': sorted_transcript_ids,
            'gene_count': gene_count,
            'gene_ids': sorted_gene_ids,
        }

    # 创建结果DataFrame
    import pandas as _pd
    result_df = _pd.DataFrame(results)
    if not result_df.empty:
        result_df = result_df.sort_values(['UTR_Features']).reset_index(drop=True)

    print(f"\n筛选规则: transcript_count == gene_count")
    print(f"组合总数: {total_groups}")
    print(f"保留组合数: {kept_groups}")
    print(f"过滤组合数: {total_groups - kept_groups}")

    # 保存结果（仅按特征）
    try:
        if output_file:
            result_df.to_csv(output_file, index=False)
            print(f"\n成功保存CSV结果到: {output_file}")
            print(f"CSV文件中每种组合最多显示 {max_csv_transcripts} 个transcript_id")

        if json_file:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(all_combinations_data, f, ensure_ascii=False, indent=2)
            print(f"成功保存完整数据到: {json_file}")

        if json_rows_file:
            with open(json_rows_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"成功保存CSV样式JSON到: {json_rows_file}")

        # 验证保存
        if output_file:
            saved_df = _pd.read_csv(output_file)
            print(f"验证成功: CSV文件包含 {len(saved_df)} 行")
        if json_file:
            with open(json_file, 'r', encoding='utf-8') as f:
                saved_json = json.load(f)
            print(f"验证成功: JSON文件包含 {len(saved_json)} 种组合")
        if json_rows_file:
            with open(json_rows_file, 'r', encoding='utf-8') as f:
                saved_rows_json = json.load(f)
            print(f"验证成功: CSV样式JSON包含 {len(saved_rows_json)} 行")

    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False

    return True

def analyze_combinations(output_file):
    """简单分析：输出过滤后组合的转录本与基因数量分布。"""
    try:
        df = pd.read_csv(output_file)
        if df.empty:
            print("无组合可分析（全部被过滤）")
            return
        # 统计每行的transcript/gene数
        df['transcript_count'] = df['transcriptId'].apply(lambda x: len(x.split(';')) if isinstance(x, str) and x else 0)
        df['gene_count'] = df['geneId'].apply(lambda x: len(x.split(';')) if isinstance(x, str) and x else 0)
        print("\n过滤后组合统计:")
        print(f"  组合数: {len(df)}")
        print(f"  transcript_count唯一值: {sorted(df['transcript_count'].unique().tolist())}")
        print(f"  gene_count唯一值: {sorted(df['gene_count'].unique().tolist())}")
    except Exception as e:
        print(f"分析组合结果时出错: {e}")

def main():
    """主函数"""
    input_file = "transcriptinfo/unique_transcript_ids_with_utr_lastest.csv"
    # 按特征输出（启用）
    output_file = "transcriptinfo/transcriptInfo_utr_combinations.csv"
    json_file = "transcriptinfo/transcriptInfo_utr_combinations_full.json"
    json_rows_file = "transcriptinfo/transcriptInfo_utr_combinations_rows.json"
    max_csv_transcripts = 999999  # CSV文件中每种组合最多保存的transcript数量

    print("转录本UTR特征组合处理工具")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"CSV输出文件: {output_file}")
    print(f"JSON输出文件(完整): {json_file}")
    print(f"JSON输出文件(行样式): {json_rows_file}")
    print(f"CSV文件中每种组合最多保存: {max_csv_transcripts} 个transcript")

    # 执行处理
    success = process_transcript_info(
        input_file,
        output_file,
        json_file,
        max_csv_transcripts,
        json_rows_file,
    )

    if success:
        # 分析结果
        analyze_combinations(output_file)

        print(f"\n处理完成！")
        print(f"CSV结果已保存到: {output_file}")
        print(f"完整数据已保存到: {json_file}")
        print(f"CSV样式JSON已保存到: {json_rows_file}")
    else:
        print(f"\n处理失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
