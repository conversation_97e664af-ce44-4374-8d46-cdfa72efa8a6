#!/usr/bin/env python3
"""
UTR特征过滤脚本
处理UTR_features_filtered.csv文件，根据transcriptId数量和UTR_Features值数量的匹配关系进行过滤
"""

import csv
import os
import sys
import json
from collections import Counter, defaultdict

def analyze_utr_features(input_file):
    """分析UTR_Features的唯一值和数量（支持CSV或JSON: 列表或字典）"""
    print(f"开始分析文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 {input_file}")
        return None

    data = []
    headers = []
    utr_features_counts = Counter()

    try:
        if input_file.lower().endswith('.json'):
            # 读取JSON，支持两种结构：
            # 1) 列表形式：[ { transcriptId, geneId, UTR_Features }, ... ]
            # 2) 字典形式：{ "yes|no|...": { transcript_ids: [...], gene_ids: [...], ... }, ... }
            with open(input_file, 'r', encoding='utf-8') as f:
                json_obj = json.load(f)

            headers = ['transcriptId', 'geneId', 'UTR_Features']

            if isinstance(json_obj, list):
                # 新输入格式（行样式JSON）
                for row in json_obj:
                    # 规范化键名，确保包含所需字段
                    norm_row = {
                        'transcriptId': str(row.get('transcriptId', '')),
                        'geneId': str(row.get('geneId', '')),
                        'UTR_Features': str(row.get('UTR_Features', '')),
                    }
                    data.append(norm_row)
                    utr_features_counts[norm_row['UTR_Features']] += 1

                print(f"成功读取JSON(行样式)，共 {len(data)} 行")
                print(f"列名: {headers}")

            elif isinstance(json_obj, dict):
                # 旧输入格式（组合键 -> 明细）
                for combination_key, entry in json_obj.items():
                    transcripts = entry.get('transcript_ids', []) or []
                    genes = entry.get('gene_ids', []) or []
                    json_transcript_count = entry.get('transcript_count', len(transcripts))
                    json_gene_count = entry.get('gene_count', len(genes))
                    row = {
                        'transcriptId': ';'.join(transcripts),
                        'geneId': ';'.join(genes),
                        'UTR_Features': combination_key,
                        'json_transcript_count': json_transcript_count,
                        'json_gene_count': json_gene_count,
                        '_combination_key': combination_key,
                        '_json_entry': entry,
                    }
                    data.append(row)
                    utr_features_counts[combination_key] = len(transcripts)

                print(f"成功读取JSON(组合映射)，共 {len(data)} 条组合")
                print(f"列名: {headers}")
            else:
                print("错误: 不支持的JSON结构（既不是列表也不是字典）")
                return None
        else:
            # 读取CSV文件
            with open(input_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                headers = reader.fieldnames
                for row in reader:
                    data.append(row)

            print(f"成功读取CSV，共 {len(data)} 行")
            print(f"列名: {headers}")

            # 检查必需的列
            required_columns = ['transcriptId', 'geneId', 'UTR_Features']
            missing_columns = [col for col in required_columns if col not in headers]
            if missing_columns:
                print(f"错误: 缺少必需的列: {missing_columns}")
                return None

            # 分析UTR_Features的唯一值（CSV场景，按出现次数计数）
            for row in data:
                utr_features_counts[row['UTR_Features']] += 1

        # 输出计数信息
        print("\n分析UTR_Features的唯一值...")
        print(f"UTR_Features唯一值数量: {len(utr_features_counts)}")
        print("\nUTR_Features值及其出现次数:")
        for feature, count in sorted(utr_features_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  '{feature}': {count} 次")

    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

    return data, headers, utr_features_counts

def filter_data(data, headers, utr_features_counts, output_file):
    """根据条件过滤数据（按UTR_Features分组：组内每行transcript数必须等于该组行数）"""
    print(f"\n开始过滤数据...")

    # 12个UTR特征列名称（用于重建features字典）
    utr_feature_columns = [
        'threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
        'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres',
        'fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
        'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres'
    ]

    def parse_features_mapping(key: str):
        """将UTR_Features字符串解析为yes/no映射"""
        if '|' in key:
            vals = [v.strip() for v in key.split('|')]
            return {col: (vals[i] if i < len(vals) else 'no') for i, col in enumerate(utr_feature_columns)}
        else:
            vals = [v.strip() for v in key.split(',')] if key else []
            return {col: ('yes' if (i < len(vals) and vals[i] == '1') else 'no') for i, col in enumerate(utr_feature_columns)}

    # 按UTR_Features分组
    groups = defaultdict(list)
    for idx, row in enumerate(data):
        groups[row.get('UTR_Features', '')].append(row)

    filtered_rows = []
    filtered_json = {}
    filter_stats = {
        'total': len(data),
        'groups_total': len(groups),
        'groups_filtered': 0,
        'groups_kept': 0,
        'rows_kept': 0,
        'rows_filtered': 0,
    }

    for key, rows in groups.items():
        row_count = len(rows)

        # 计算每行的transcript数
        per_row_counts = []
        all_transcripts = set()
        all_genes = set()

        for row in rows:
            transcript_ids = [tid for tid in str(row.get('transcriptId', '')).split(';') if tid]
            gene_ids = [gid for gid in str(row.get('geneId', '')).split(';') if gid]
            per_row_counts.append(len(transcript_ids))
            all_transcripts.update(transcript_ids)
            all_genes.update(gene_ids)

        # 验证条件：该 UTR_Features 唯一值的行数 == 每行中transcript的数量（组内每行都需满足）
        keep_group = all(c == row_count for c in per_row_counts)

        if not keep_group:
            filter_stats['groups_filtered'] += 1
            filter_stats['rows_filtered'] += row_count
            continue

        # 保留该组所有行
        filtered_rows.extend(rows)
        filter_stats['groups_kept'] += 1
        filter_stats['rows_kept'] += row_count

        # 生成汇总JSON（按组合键）
        features_map = parse_features_mapping(key)
        filtered_json[key] = {
            'features': features_map,
            'transcript_count': len(all_transcripts),
            'transcript_ids': sorted(all_transcripts),
            'gene_count': len(all_genes),
            'gene_ids': sorted(all_genes),
        }

    # 保存过滤后的数据（JSON，按组合键聚合）
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_json, f, ensure_ascii=False, indent=2)
        print(f"\n过滤后的数据已保存到(JSON): {output_file}")
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return None

    # 显示过滤统计
    print(f"\n过滤统计:")
    print(f"  总行数: {filter_stats['total']}")
    print(f"  唯一UTR_Features组数: {filter_stats['groups_total']}")
    print(f"  保留组数: {filter_stats['groups_kept']}")
    print(f"  过滤组数: {filter_stats['groups_filtered']}")
    print(f"  保留行数: {filter_stats['rows_kept']}")
    print(f"  过滤行数: {filter_stats['rows_filtered']}")
    kept_rate = (filter_stats['rows_kept'] / filter_stats['total'] * 100) if filter_stats['total'] else 0
    print(f"  保留率: {kept_rate:.2f}%")

    return filtered_rows, filter_stats

def analyze_filtered_data(filtered_data):
    """分析过滤后的数据"""
    if len(filtered_data) == 0:
        print("\n过滤后没有数据保留")
        return

    print(f"\n分析过滤后的数据...")

    # 分析transcriptId数量分布
    transcript_counts = []
    for row in filtered_data:
        transcript_ids = row['transcriptId'].split(';')
        transcript_counts.append(len(transcript_ids))

    transcript_count_dist = Counter(transcript_counts)
    print(f"\n过滤后transcriptId数量分布:")
    for count, freq in sorted(transcript_count_dist.items()):
        print(f"  {count}个transcript: {freq} 行")

    print(f"\ntranscriptId数量统计:")
    print(f"  最小值: {min(transcript_counts)}")
    print(f"  最大值: {max(transcript_counts)}")
    print(f"  平均值: {sum(transcript_counts)/len(transcript_counts):.2f}")

    # 分析过滤后的UTR_Features分布
    utr_features_counts = Counter()
    for row in filtered_data:
        utr_features_counts[row['UTR_Features']] += 1

    print(f"\n过滤后UTR_Features分布 (前10个):")
    for feature, count in sorted(utr_features_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  '{feature}': {count} 次")

def main():
    """主函数"""
    input_file = "transcriptinfo/transcriptInfo_utr_combinations_rows.json"
    output_file = "transcriptinfo/UTR_features_filtered_processed.json"
    
    print("UTR特征过滤工具")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件(JSON): {output_file}")
    
    # 分析原始数据
    result = analyze_utr_features(input_file)
    if result is None:
        print("分析失败，程序退出")
        sys.exit(1)

    data, headers, utr_features_counts = result

    # 过滤数据
    filter_result = filter_data(data, headers, utr_features_counts, output_file)
    if filter_result is None:
        print("过滤失败，程序退出")
        sys.exit(1)

    filtered_data, filter_stats = filter_result

    # 分析过滤后的数据
    analyze_filtered_data(filtered_data)
    
    print("\n" + "=" * 50)
    print("处理完成！")
    print(f"原始数据: {filter_stats['total']} 行")
    print(f"过滤后数据: {filter_stats['rows_kept']} 行")
    print(f"输出文件(JSON): {output_file}")

if __name__ == "__main__":
    main()
